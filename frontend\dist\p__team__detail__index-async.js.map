{"version": 3, "sources": ["src/pages/team/detail/components/EnhancedTeamDetail.tsx", "src/pages/team/detail/index.tsx"], "sourcesContent": ["/**\n * 增强版团队详情组件 - 全新UI设计\n */\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  Button,\n  Space,\n  Typography,\n  Avatar,\n  Tag,\n  Progress,\n  Tooltip,\n  Badge,\n  Modal,\n  Form,\n  Input,\n  message\n} from 'antd';\nimport {\n  TeamOutlined,\n  UserOutlined,\n  CalendarOutlined,\n  EditOutlined,\n  CrownOutlined,\n  ClockCircleOutlined,\n  SettingOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined,\n  ArrowLeftOutlined\n} from '@ant-design/icons';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\nimport { history, useModel } from '@umijs/max';\nimport TeamMemberList from './TeamMemberList';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\n\ninterface EnhancedTeamDetailProps {\n  teamDetail: TeamDetailResponse;\n  loading: boolean;\n  onRefresh: () => void;\n}\n\nconst EnhancedTeamDetail: React.FC<EnhancedTeamDetailProps> = ({\n  teamDetail,\n  loading,\n  onRefresh\n}) => {\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [deleting, setDeleting] = useState(false);\n  const [form] = Form.useForm();\n  const { setInitialState } = useModel('@@initialState');\n\n  const handleEdit = () => {\n    form.setFieldsValue({\n      name: teamDetail.name,\n      description: teamDetail.description || '',\n    });\n    setEditModalVisible(true);\n  };\n\n  const handleUpdateTeam = async (values: UpdateTeamRequest) => {\n    try {\n      setUpdating(true);\n      await TeamService.updateCurrentTeam(values);\n      message.success('团队信息更新成功');\n      setEditModalVisible(false);\n      onRefresh();\n    } catch (error) {\n      console.error('更新团队失败:', error);\n      message.error('更新团队失败');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  const handleDeleteTeam = () => {\n    Modal.confirm({\n      title: '确认删除团队',\n      content: '删除团队后，所有团队数据将无法恢复。确定要删除吗？',\n      icon: <ExclamationCircleOutlined />,\n      okText: '确认删除',\n      cancelText: '取消',\n      okType: 'danger',\n      onOk: async () => {\n        try {\n          setDeleting(true);\n          // 注意：这里需要根据实际API调整\n          await TeamService.deleteCurrentTeam();\n          message.success('团队删除成功');\n          // 更新全局状态，清除当前团队\n          setInitialState((s) => ({ ...s, currentTeam: undefined }));\n          history.push('/user/team-select');\n        } catch (error) {\n          console.error('删除团队失败:', error);\n          message.error('删除团队失败');\n        } finally {\n          setDeleting(false);\n        }\n      },\n    });\n  };\n\n  const handleGoBack = () => {\n    history.push('/user/team-select');\n  };\n\n\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getTeamStatusColor = () => {\n    const memberCount = teamDetail.memberCount;\n    if (memberCount >= 10) return '#52c41a'; // 绿色 - 活跃\n    if (memberCount >= 5) return '#faad14'; // 橙色 - 正常\n    return '#1890ff'; // 蓝色 - 小团队\n  };\n\n  const getTeamStatusText = () => {\n    const memberCount = teamDetail.memberCount;\n    if (memberCount >= 10) return '活跃团队';\n    if (memberCount >= 5) return '正常团队';\n    return '小型团队';\n  };\n\n  return (\n    <div style={{ padding: '0 24px' }}>\n      {/* 团队头部信息卡片 */}\n      <Card \n        style={{ \n          marginBottom: 24,\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          border: 'none',\n          borderRadius: 16\n        }}\n        bodyStyle={{ padding: '32px' }}\n      >\n        <Row align=\"middle\" justify=\"space-between\">\n          <Col>\n            <Space size=\"large\" align=\"center\">\n              <Button\n                type=\"text\"\n                icon={<ArrowLeftOutlined />}\n                onClick={handleGoBack}\n                style={{\n                  color: 'rgba(255, 255, 255, 0.8)',\n                  fontSize: 16,\n                  padding: '4px 8px'\n                }}\n              >\n                返回\n              </Button>\n              <Avatar\n                size={80}\n                icon={<TeamOutlined />}\n                style={{\n                  backgroundColor: 'rgba(255, 255, 255, 0.2)',\n                  color: 'white',\n                  fontSize: 32\n                }}\n              />\n              <div>\n                <Space align=\"center\" style={{ marginBottom: 8 }}>\n                  <Title level={2} style={{ color: 'white', margin: 0 }}>\n                    {teamDetail.name}\n                  </Title>\n                  {teamDetail.isCreator && (\n                    <Tag\n                      icon={<CrownOutlined />}\n                      color=\"gold\"\n                      style={{ fontSize: 12 }}\n                    >\n                      管理员\n                    </Tag>\n                  )}\n                  <Badge\n                    color={getTeamStatusColor()}\n                    text={\n                      <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>\n                        {getTeamStatusText()}\n                      </Text>\n                    }\n                  />\n                </Space>\n                <Paragraph\n                  style={{\n                    color: 'rgba(255, 255, 255, 0.8)',\n                    margin: 0,\n                    maxWidth: 400\n                  }}\n                  ellipsis={{ rows: 2 }}\n                >\n                  {teamDetail.description || '这个团队还没有描述'}\n                </Paragraph>\n              </div>\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              {teamDetail.isCreator && (\n                <>\n                  <Button\n                    size=\"large\"\n                    icon={<EditOutlined />}\n                    onClick={handleEdit}\n                    style={{\n                      background: 'rgba(255, 255, 255, 0.1)',\n                      borderColor: 'rgba(255, 255, 255, 0.2)',\n                      color: 'white'\n                    }}\n                  >\n                    编辑团队\n                  </Button>\n                  <Button\n                    size=\"large\"\n                    icon={<DeleteOutlined />}\n                    onClick={handleDeleteTeam}\n                    danger\n                    style={{\n                      background: 'rgba(255, 77, 79, 0.2)',\n                      borderColor: 'rgba(255, 77, 79, 0.4)',\n                      color: '#ff4d4f'\n                    }}\n                  >\n                    删除团队\n                  </Button>\n                </>\n              )}\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 团队统计信息 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"团队成员\"\n              value={teamDetail.memberCount}\n              suffix=\"人\"\n              prefix={<UserOutlined style={{ color: '#1890ff' }} />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"创建时间\"\n              value={formatDate(teamDetail.createdAt)}\n              prefix={<CalendarOutlined style={{ color: '#52c41a' }} />}\n              valueStyle={{ color: '#52c41a', fontSize: 16 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"最后活动\"\n              value={formatDate(teamDetail.updatedAt)}\n              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}\n              valueStyle={{ color: '#faad14', fontSize: 16 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <div style={{ textAlign: 'center' }}>\n              <Text type=\"secondary\" style={{ fontSize: 14 }}>团队活跃度</Text>\n              <div style={{ marginTop: 8 }}>\n                <Progress\n                  type=\"circle\"\n                  size={60}\n                  percent={Math.min(teamDetail.memberCount * 10, 100)}\n                  strokeColor={getTeamStatusColor()}\n                  format={() => (\n                    <Text style={{ fontSize: 12, color: getTeamStatusColor() }}>\n                      {teamDetail.memberCount >= 10 ? '高' : \n                       teamDetail.memberCount >= 5 ? '中' : '低'}\n                    </Text>\n                  )}\n                />\n              </div>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 团队成员列表 */}\n      <TeamMemberList \n        teamId={teamDetail.id}\n        isCreator={teamDetail.isCreator}\n        onMemberChange={onRefresh}\n      />\n\n\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleUpdateTeam}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称' },\n              { max: 50, message: '团队名称不能超过50个字符' }\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[\n              { max: 200, message: '团队描述不能超过200个字符' }\n            ]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入团队描述（可选）\"\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => setEditModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={updating}>\n                保存\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default EnhancedTeamDetail;\n", "/**\n * 团队详情页面\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Typography,\n  message,\n  Spin\n} from 'antd';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse } from '@/types/api';\nimport EnhancedTeamDetail from './components/EnhancedTeamDetail';\n\nconst { Text } = Typography;\n\nconst TeamDetailPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n\n  useEffect(() => {\n    fetchTeamDetail();\n  }, []);\n\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      message.error('获取团队详情失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Spin size=\"large\" />\n        </div>\n      </PageContainer>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Text type=\"secondary\">团队信息加载失败</Text>\n        </div>\n      </PageContainer>\n    );\n  }\n\n  return (\n    <PageContainer style={{ background: '#f5f5f5' }}>\n      <EnhancedTeamDetail\n        teamDetail={teamDetail}\n        loading={loading}\n        onRefresh={fetchTeamDetail}\n      />\n    </PageContainer>\n  );\n};\n\nexport default TeamDetailPage;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BA0WD;;;eAAA;;;;;;;0DAxWgC;6BAkBzB;8BAYA;iCACqB;4BAEM;kEACP;;;;;;;;;;AAE3B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;AAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAQ1B,MAAM,qBAAwD,CAAC,EAC7D,UAAU,EACV,OAAO,EACP,SAAS,EACV;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAC3B,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IAErC,MAAM,aAAa;QACjB,KAAK,cAAc,CAAC;YAClB,MAAM,WAAW,IAAI;YACrB,aAAa,WAAW,WAAW,IAAI;QACzC;QACA,oBAAoB;IACtB;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,YAAY;YACZ,MAAM,qBAAW,CAAC,iBAAiB,CAAC;YACpC,aAAO,CAAC,OAAO,CAAC;YAChB,oBAAoB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,mBAAmB;QACvB,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS;YACT,MAAM,2BAAC,gCAAyB;;;;;YAChC,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,MAAM;gBACJ,IAAI;oBACF,YAAY;oBAEZ,MAAM,qBAAW,CAAC,iBAAiB;oBACnC,aAAO,CAAC,OAAO,CAAC;oBAEhB,gBAAgB,CAAC,IAAO,CAAA;4BAAE,GAAG,CAAC;4BAAE,aAAa;wBAAU,CAAA;oBACvD,YAAO,CAAC,IAAI,CAAC;gBACf,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;oBACzB,aAAO,CAAC,KAAK,CAAC;gBAChB,SAAU;oBACR,YAAY;gBACd;YACF;QACF;IACF;IAEA,MAAM,eAAe;QACnB,YAAO,CAAC,IAAI,CAAC;IACf;IAIA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,qBAAqB;QACzB,MAAM,cAAc,WAAW,WAAW;QAC1C,IAAI,eAAe,IAAI,OAAO;QAC9B,IAAI,eAAe,GAAG,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB,MAAM,cAAc,WAAW,WAAW;QAC1C,IAAI,eAAe,IAAI,OAAO;QAC9B,IAAI,eAAe,GAAG,OAAO;QAC7B,OAAO;IACT;IAEA,OACE,2BAAC;QAAI,OAAO;YAAE,SAAS;QAAS;;YAE9B,2BAAC,UAAI;gBACH,OAAO;oBACL,cAAc;oBACd,YAAY;oBACZ,QAAQ;oBACR,cAAc;gBAChB;gBACA,WAAW;oBAAE,SAAS;gBAAO;0BAE7B,2BAAC,SAAG;oBAAC,OAAM;oBAAS,SAAQ;;wBAC1B,2BAAC,SAAG;sCACF,2BAAC,WAAK;gCAAC,MAAK;gCAAQ,OAAM;;oCACxB,2BAAC,YAAM;wCACL,MAAK;wCACL,MAAM,2BAAC,wBAAiB;;;;;wCACxB,SAAS;wCACT,OAAO;4CACL,OAAO;4CACP,UAAU;4CACV,SAAS;wCACX;kDACD;;;;;;oCAGD,2BAAC,YAAM;wCACL,MAAM;wCACN,MAAM,2BAAC,mBAAY;;;;;wCACnB,OAAO;4CACL,iBAAiB;4CACjB,OAAO;4CACP,UAAU;wCACZ;;;;;;oCAEF,2BAAC;;4CACC,2BAAC,WAAK;gDAAC,OAAM;gDAAS,OAAO;oDAAE,cAAc;gDAAE;;oDAC7C,2BAAC;wDAAM,OAAO;wDAAG,OAAO;4DAAE,OAAO;4DAAS,QAAQ;wDAAE;kEACjD,WAAW,IAAI;;;;;;oDAEjB,WAAW,SAAS,IACnB,2BAAC,SAAG;wDACF,MAAM,2BAAC,oBAAa;;;;;wDACpB,OAAM;wDACN,OAAO;4DAAE,UAAU;wDAAG;kEACvB;;;;;;oDAIH,2BAAC,WAAK;wDACJ,OAAO;wDACP,MACE,2BAAC;4DAAK,OAAO;gEAAE,OAAO;4DAA2B;sEAC9C;;;;;;;;;;;;;;;;;4CAKT,2BAAC;gDACC,OAAO;oDACL,OAAO;oDACP,QAAQ;oDACR,UAAU;gDACZ;gDACA,UAAU;oDAAE,MAAM;gDAAE;0DAEnB,WAAW,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;wBAKnC,2BAAC,SAAG;sCACF,2BAAC,WAAK;0CACH,WAAW,SAAS,IACnB;;wCACE,2BAAC,YAAM;4CACL,MAAK;4CACL,MAAM,2BAAC,mBAAY;;;;;4CACnB,SAAS;4CACT,OAAO;gDACL,YAAY;gDACZ,aAAa;gDACb,OAAO;4CACT;sDACD;;;;;;wCAGD,2BAAC,YAAM;4CACL,MAAK;4CACL,MAAM,2BAAC,qBAAc;;;;;4CACrB,SAAS;4CACT,MAAM;4CACN,OAAO;gDACL,YAAY;gDACZ,aAAa;gDACb,OAAO;4CACT;sDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWb,2BAAC,SAAG;gBAAC,QAAQ;oBAAC;oBAAI;iBAAG;gBAAE,OAAO;oBAAE,cAAc;gBAAG;;oBAC/C,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,2BAAC,UAAI;sCACH,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW;gCAC7B,QAAO;gCACP,QAAQ,2BAAC,mBAAY;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCAChD,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;oBAIrC,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,2BAAC,UAAI;sCACH,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW,SAAS;gCACtC,QAAQ,2BAAC,uBAAgB;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCACpD,YAAY;oCAAE,OAAO;oCAAW,UAAU;gCAAG;;;;;;;;;;;;;;;;oBAInD,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,2BAAC,UAAI;sCACH,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW,SAAS;gCACtC,QAAQ,2BAAC,0BAAmB;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCACvD,YAAY;oCAAE,OAAO;oCAAW,UAAU;gCAAG;;;;;;;;;;;;;;;;oBAInD,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,2BAAC,UAAI;sCACH,2BAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAS;;oCAChC,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,UAAU;wCAAG;kDAAG;;;;;;oCAChD,2BAAC;wCAAI,OAAO;4CAAE,WAAW;wCAAE;kDACzB,2BAAC,cAAQ;4CACP,MAAK;4CACL,MAAM;4CACN,SAAS,KAAK,GAAG,CAAC,WAAW,WAAW,GAAG,IAAI;4CAC/C,aAAa;4CACb,QAAQ,IACN,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,OAAO;oDAAqB;8DACtD,WAAW,WAAW,IAAI,KAAK,MAC/B,WAAW,WAAW,IAAI,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWrD,2BAAC,uBAAc;gBACb,QAAQ,WAAW,EAAE;gBACrB,WAAW,WAAW,SAAS;gBAC/B,gBAAgB;;;;;;YAMlB,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,oBAAoB;gBACpC,QAAQ;gBACR,OAAO;0BAEP,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;wBAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAU;gCACrC;oCAAE,KAAK;oCAAI,SAAS;gCAAgB;6BACrC;sCAED,2BAAC,WAAK;gCAAC,aAAY;;;;;;;;;;;wBAErB,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,KAAK;oCAAK,SAAS;gCAAiB;6BACvC;sCAED,2BAAC;gCACC,MAAM;gCACN,aAAY;gCACZ,SAAS;gCACT,WAAW;;;;;;;;;;;wBAGf,2BAAC,UAAI,CAAC,IAAI;4BAAC,OAAO;gCAAE,cAAc;gCAAG,WAAW;4BAAQ;sCACtD,2BAAC,WAAK;;oCACJ,2BAAC,YAAM;wCAAC,SAAS,IAAM,oBAAoB;kDAAQ;;;;;;oCAGnD,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;wCAAS,SAAS;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1E;GAzTM;;QAQW,UAAI,CAAC;QACQ,aAAQ;;;KAThC;IA2TN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5Wf;;CAEC;;;;4BAmED;;;eAAA;;;;;;;0DAjE2C;6BAKpC;sCACuB;iCACF;sEAEG;;;;;;;;;;AAE/B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAE3B,MAAM,iBAA2B;;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAA4B;IAExE,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,MAAM,qBAAW,CAAC,oBAAoB;YACrD,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SACF,OACE,2BAAC,4BAAa;kBACZ,2BAAC;YAAI,OAAO;gBAAE,WAAW;gBAAU,SAAS;YAAS;sBACnD,2BAAC,UAAI;gBAAC,MAAK;;;;;;;;;;;;;;;;IAMnB,IAAI,CAAC,YACH,OACE,2BAAC,4BAAa;kBACZ,2BAAC;YAAI,OAAO;gBAAE,WAAW;gBAAU,SAAS;YAAS;sBACnD,2BAAC;gBAAK,MAAK;0BAAY;;;;;;;;;;;;;;;;IAM/B,OACE,2BAAC,4BAAa;QAAC,OAAO;YAAE,YAAY;QAAU;kBAC5C,2BAAC,2BAAkB;YACjB,YAAY;YACZ,SAAS;YACT,WAAW;;;;;;;;;;;AAInB;GAlDM;KAAA;IAoDN,WAAe"}