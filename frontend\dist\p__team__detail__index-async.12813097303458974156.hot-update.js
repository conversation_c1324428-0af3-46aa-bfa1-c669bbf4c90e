globalThis.makoModuleHotUpdate('p__team__detail__index', {
    modules: {
        "src/pages/team/detail/components/EnhancedTeamDetail.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _services = __mako_require__("src/services/index.ts");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _TeamMemberList = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamMemberList.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text, Paragraph } = _antd.Typography;
            const { TextArea } = _antd.Input;
            const EnhancedTeamDetail = ({ teamDetail, loading, onRefresh })=>{
                _s();
                const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
                const [updating, setUpdating] = (0, _react.useState)(false);
                const [deleting, setDeleting] = (0, _react.useState)(false);
                const [form] = _antd.Form.useForm();
                const { setInitialState } = (0, _max.useModel)('@@initialState');
                const handleEdit = ()=>{
                    form.setFieldsValue({
                        name: teamDetail.name,
                        description: teamDetail.description || ''
                    });
                    setEditModalVisible(true);
                };
                const handleUpdateTeam = async (values)=>{
                    try {
                        setUpdating(true);
                        await _services.TeamService.updateCurrentTeam(values);
                        _antd.message.success('团队信息更新成功');
                        setEditModalVisible(false);
                        onRefresh();
                    } catch (error) {
                        console.error('更新团队失败:', error);
                        _antd.message.error('更新团队失败');
                    } finally{
                        setUpdating(false);
                    }
                };
                const handleDeleteTeam = ()=>{
                    _antd.Modal.confirm({
                        title: '确认删除团队',
                        content: '删除团队后，所有团队数据将无法恢复。确定要删除吗？',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 88,
                            columnNumber: 13
                        }, this),
                        okText: '确认删除',
                        cancelText: '取消',
                        okType: 'danger',
                        onOk: async ()=>{
                            try {
                                setDeleting(true);
                                // 注意：这里需要根据实际API调整
                                await _services.TeamService.deleteCurrentTeam();
                                _antd.message.success('团队删除成功');
                                // 更新全局状态，清除当前团队
                                setInitialState((s)=>({
                                        ...s,
                                        currentTeam: undefined
                                    }));
                                _max.history.push('/user/team-select');
                            } catch (error) {
                                console.error('删除团队失败:', error);
                                _antd.message.error('删除团队失败');
                            } finally{
                                setDeleting(false);
                            }
                        }
                    });
                };
                const handleGoBack = ()=>{
                    _max.history.push('/user/team-select');
                };
                const formatDate = (dateString)=>{
                    return new Date(dateString).toLocaleDateString('zh-CN', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    });
                };
                const getTeamStatusColor = ()=>{
                    const memberCount = teamDetail.memberCount;
                    if (memberCount >= 10) return '#52c41a'; // 绿色 - 活跃
                    if (memberCount >= 5) return '#faad14'; // 橙色 - 正常
                    return '#1890ff'; // 蓝色 - 小团队
                };
                const getTeamStatusText = ()=>{
                    const memberCount = teamDetail.memberCount;
                    if (memberCount >= 10) return '活跃团队';
                    if (memberCount >= 5) return '正常团队';
                    return '小型团队';
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        padding: '0 24px'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            style: {
                                marginBottom: 24,
                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                border: 'none',
                                borderRadius: 16
                            },
                            bodyStyle: {
                                padding: '32px'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                align: "middle",
                                justify: "space-between",
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            size: "large",
                                            align: "center",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "text",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ArrowLeftOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                        lineNumber: 156,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    onClick: handleGoBack,
                                                    style: {
                                                        color: 'rgba(255, 255, 255, 0.8)',
                                                        fontSize: 16,
                                                        padding: '4px 8px'
                                                    },
                                                    children: "返回"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 154,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                    size: 80,
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                        lineNumber: 168,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    style: {
                                                        backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                                        color: 'white',
                                                        fontSize: 32
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 166,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            align: "center",
                                                            style: {
                                                                marginBottom: 8
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                    level: 2,
                                                                    style: {
                                                                        color: 'white',
                                                                        margin: 0
                                                                    },
                                                                    children: teamDetail.name
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                    lineNumber: 177,
                                                                    columnNumber: 19
                                                                }, this),
                                                                teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                        lineNumber: 182,
                                                                        columnNumber: 29
                                                                    }, void 0),
                                                                    color: "gold",
                                                                    style: {
                                                                        fontSize: 12
                                                                    },
                                                                    children: "管理员"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                    lineNumber: 181,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                                                                    color: getTeamStatusColor(),
                                                                    text: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            color: 'rgba(255, 255, 255, 0.8)'
                                                                        },
                                                                        children: getTeamStatusText()
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                        lineNumber: 192,
                                                                        columnNumber: 23
                                                                    }, void 0)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                    lineNumber: 189,
                                                                    columnNumber: 19
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                            lineNumber: 176,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                            style: {
                                                                color: 'rgba(255, 255, 255, 0.8)',
                                                                margin: 0,
                                                                maxWidth: 400
                                                            },
                                                            ellipsis: {
                                                                rows: 2
                                                            },
                                                            children: teamDetail.description || '这个团队还没有描述'
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                            lineNumber: 198,
                                                            columnNumber: 17
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 175,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 153,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 152,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                        size: "large",
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                            lineNumber: 217,
                                                            columnNumber: 27
                                                        }, void 0),
                                                        onClick: handleEdit,
                                                        style: {
                                                            background: 'rgba(255, 255, 255, 0.1)',
                                                            borderColor: 'rgba(255, 255, 255, 0.2)',
                                                            color: 'white'
                                                        },
                                                        children: "编辑团队"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                        lineNumber: 215,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                        size: "large",
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                            lineNumber: 229,
                                                            columnNumber: 27
                                                        }, void 0),
                                                        onClick: handleDeleteTeam,
                                                        danger: true,
                                                        style: {
                                                            background: 'rgba(255, 77, 79, 0.2)',
                                                            borderColor: 'rgba(255, 77, 79, 0.4)',
                                                            color: '#ff4d4f'
                                                        },
                                                        children: "删除团队"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                        lineNumber: 227,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true)
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 212,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 211,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                lineNumber: 151,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 142,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: [
                                16,
                                16
                            ],
                            style: {
                                marginBottom: 24
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "团队成员",
                                            value: teamDetail.memberCount,
                                            suffix: "人",
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                style: {
                                                    color: '#1890ff'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 255,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#1890ff'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 251,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 250,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 249,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "创建时间",
                                            value: formatDate(teamDetail.createdAt),
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                style: {
                                                    color: '#52c41a'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 265,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#52c41a',
                                                fontSize: 16
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 262,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 261,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 260,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "最后活动",
                                            value: formatDate(teamDetail.updatedAt),
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                style: {
                                                    color: '#faad14'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 275,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#faad14',
                                                fontSize: 16
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 272,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 271,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 270,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                textAlign: 'center'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    style: {
                                                        fontSize: 14
                                                    },
                                                    children: "团队活跃度"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 283,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        marginTop: 8
                                                    },
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                                        type: "circle",
                                                        size: 60,
                                                        percent: Math.min(teamDetail.memberCount * 10, 100),
                                                        strokeColor: getTeamStatusColor(),
                                                        format: ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 12,
                                                                    color: getTeamStatusColor()
                                                                },
                                                                children: teamDetail.memberCount >= 10 ? '高' : teamDetail.memberCount >= 5 ? '中' : '低'
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                lineNumber: 291,
                                                                columnNumber: 21
                                                            }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                        lineNumber: 285,
                                                        columnNumber: 17
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 284,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 282,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 281,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 280,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 248,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamMemberList.default, {
                            teamId: teamDetail.id,
                            isCreator: teamDetail.isCreator,
                            onMemberChange: onRefresh
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 304,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "编辑团队信息",
                            open: editModalVisible,
                            onCancel: ()=>setEditModalVisible(false),
                            footer: null,
                            width: 600,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: form,
                                layout: "vertical",
                                onFinish: handleUpdateTeam,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "团队名称",
                                        name: "name",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入团队名称'
                                            },
                                            {
                                                max: 50,
                                                message: '团队名称不能超过50个字符'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入团队名称"
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 333,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 325,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "团队描述",
                                        name: "description",
                                        rules: [
                                            {
                                                max: 200,
                                                message: '团队描述不能超过200个字符'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            rows: 4,
                                            placeholder: "请输入团队描述（可选）",
                                            showCount: true,
                                            maxLength: 200
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 342,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 335,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        style: {
                                            marginBottom: 0,
                                            textAlign: 'right'
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: ()=>setEditModalVisible(false),
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 351,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    loading: updating,
                                                    children: "保存"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 354,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 350,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 349,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                lineNumber: 320,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 313,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                    lineNumber: 140,
                    columnNumber: 5
                }, this);
            };
            _s(EnhancedTeamDetail, "UalB0vhOvdT9JI/tG4E1rE8Z8wk=", false, function() {
                return [
                    _antd.Form.useForm,
                    _max.useModel
                ];
            });
            _c = EnhancedTeamDetail;
            var _default = EnhancedTeamDetail;
            var _c;
            $RefreshReg$(_c, "EnhancedTeamDetail");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '15357805829218415678';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/create/index.tsx": [
            "p__team__create__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "common",
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=p__team__detail__index-async.12813097303458974156.hot-update.js.map