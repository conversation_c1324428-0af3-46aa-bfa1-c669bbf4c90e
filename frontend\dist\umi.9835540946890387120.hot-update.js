globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/.umi/core/route.tsx": function(module, exports, __mako_require__) {
            "use strict";
            var interop = __mako_require__("@swc/helpers/_/_interop_require_wildcard")._;
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "getRoutes", {
                enumerable: true,
                get: function() {
                    return getRoutes;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            async function getRoutes() {
                const routes = {
                    "1": {
                        "path": "/user",
                        "layout": false,
                        "id": "1"
                    },
                    "2": {
                        "name": "login",
                        "path": "/user/login",
                        "parentId": "1",
                        "id": "2"
                    },
                    "3": {
                        "name": "team-select",
                        "path": "/user/team-select",
                        "parentId": "1",
                        "id": "3"
                    },
                    "4": {
                        "path": "/team/create",
                        "layout": false,
                        "id": "4"
                    },
                    "5": {
                        "path": "/dashboard",
                        "name": "仪表盘",
                        "icon": "dashboard",
                        "parentId": "ant-design-pro-layout",
                        "id": "5"
                    },
                    "6": {
                        "path": "/team",
                        "name": "团队管理",
                        "icon": "team",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "6"
                    },
                    "7": {
                        "path": "/team/detail",
                        "name": "团队详情",
                        "icon": "team",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "7"
                    },
                    "8": {
                        "path": "/personal-center",
                        "name": "个人中心",
                        "icon": "user",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "8"
                    },
                    "9": {
                        "path": "/user-manage",
                        "name": "用户管理",
                        "icon": "user",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "9"
                    },
                    "10": {
                        "path": "/subscription",
                        "name": "订阅管理",
                        "icon": "crown",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "10"
                    },
                    "11": {
                        "path": "/friend",
                        "name": "好友管理",
                        "icon": "userAdd",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "11"
                    },
                    "12": {
                        "path": "/help",
                        "name": "帮助中心",
                        "icon": "question",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "12"
                    },
                    "13": {
                        "path": "/",
                        "redirect": "/dashboard",
                        "parentId": "ant-design-pro-layout",
                        "id": "13"
                    },
                    "14": {
                        "path": "*",
                        "layout": false,
                        "id": "14"
                    },
                    "ant-design-pro-layout": {
                        "id": "ant-design-pro-layout",
                        "path": "/",
                        "isLayout": true
                    },
                    "umi/plugin/openapi": {
                        "path": "/umi/plugin/openapi",
                        "id": "umi/plugin/openapi"
                    }
                };
                return {
                    routes,
                    routeComponents: {
                        '1': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/.umi/core/EmptyRoute.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/.umi/core/EmptyRoute.tsx")))),
                        '2': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/pages/user/login/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/user/login/index.tsx")))),
                        '3': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/pages/user/team-select/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/user/team-select/index.tsx")))),
                        '4': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/pages/team/create/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/team/create/index.tsx")))),
                        '5': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/pages/Dashboard/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/Dashboard/index.tsx")))),
                        '6': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/pages/team/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/team/index.tsx")))),
                        '7': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/pages/team/detail/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/team/detail/index.tsx")))),
                        '8': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/pages/personal-center/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/personal-center/index.tsx")))),
                        '9': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/pages/user/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/user/index.tsx")))),
                        '10': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/pages/subscription/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/subscription/index.tsx")))),
                        '11': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/pages/friend/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/friend/index.tsx")))),
                        '12': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/pages/help/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/help/index.tsx")))),
                        '13': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/.umi/core/EmptyRoute.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/.umi/core/EmptyRoute.tsx")))),
                        '14': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/pages/404.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/404.tsx")))),
                        'ant-design-pro-layout': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/.umi/plugin-layout/Layout.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/.umi/plugin-layout/Layout.tsx")))),
                        'umi/plugin/openapi': /*#__PURE__*/ _react.default.lazy(()=>__mako_require__.ensure2("src/.umi/plugin-openapi/openapi.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/.umi/plugin-openapi/openapi.tsx"))))
                    }
                };
            }
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/team/detail/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _services = __mako_require__("src/services/index.ts");
            var _EnhancedTeamDetail = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/EnhancedTeamDetail.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text } = _antd.Typography;
            const TeamDetailPage = ()=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(true);
                const [teamDetail, setTeamDetail] = (0, _react.useState)(null);
                (0, _react.useEffect)(()=>{
                    fetchTeamDetail();
                }, []);
                const fetchTeamDetail = async ()=>{
                    try {
                        setLoading(true);
                        const detail = await _services.TeamService.getCurrentTeamDetail();
                        setTeamDetail(detail);
                    } catch (error) {
                        console.error('获取团队详情失败:', error);
                        _antd.message.error('获取团队详情失败');
                    } finally{
                        setLoading(false);
                    }
                };
                if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '50px 0'
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            size: "large"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 43,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 42,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/detail/index.tsx",
                    lineNumber: 41,
                    columnNumber: 7
                }, this);
                if (!teamDetail) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '50px 0'
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "团队信息加载失败"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 53,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 52,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/detail/index.tsx",
                    lineNumber: 51,
                    columnNumber: 7
                }, this);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    style: {
                        background: '#f5f5f5'
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_EnhancedTeamDetail.default, {
                        teamDetail: teamDetail,
                        loading: loading,
                        onRefresh: fetchTeamDetail
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 61,
                        columnNumber: 7
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/detail/index.tsx",
                    lineNumber: 60,
                    columnNumber: 5
                }, this);
            };
            _s(TeamDetailPage, "VLkJGs2Bd2ha+V0CkOUJQHy1qgw=");
            _c = TeamDetailPage;
            var _default = TeamDetailPage;
            var _c;
            $RefreshReg$(_c, "TeamDetailPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/team/detail/components/EnhancedTeamDetail.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _services = __mako_require__("src/services/index.ts");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _TeamMemberList = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamMemberList.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text, Paragraph } = _antd.Typography;
            const { TextArea } = _antd.Input;
            const EnhancedTeamDetail = ({ teamDetail, loading, onRefresh })=>{
                _s();
                const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
                const [updating, setUpdating] = (0, _react.useState)(false);
                const [deleting, setDeleting] = (0, _react.useState)(false);
                const [form] = _antd.Form.useForm();
                const { setInitialState } = (0, _max.useModel)('@@initialState');
                const handleEdit = ()=>{
                    form.setFieldsValue({
                        name: teamDetail.name,
                        description: teamDetail.description || ''
                    });
                    setEditModalVisible(true);
                };
                const handleUpdateTeam = async (values)=>{
                    try {
                        setUpdating(true);
                        await _services.TeamService.updateCurrentTeam(values);
                        _antd.message.success('团队信息更新成功');
                        setEditModalVisible(false);
                        onRefresh();
                    } catch (error) {
                        console.error('更新团队失败:', error);
                        _antd.message.error('更新团队失败');
                    } finally{
                        setUpdating(false);
                    }
                };
                const handleDeleteTeam = ()=>{
                    _antd.Modal.confirm({
                        title: '确认删除团队',
                        content: '删除团队后，所有团队数据将无法恢复。确定要删除吗？',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 91,
                            columnNumber: 13
                        }, this),
                        okText: '确认删除',
                        cancelText: '取消',
                        okType: 'danger',
                        onOk: async ()=>{
                            try {
                                setDeleting(true);
                                // 注意：这里需要根据实际API调整
                                await _services.TeamService.deleteCurrentTeam();
                                _antd.message.success('团队删除成功');
                                // 更新全局状态，清除当前团队
                                setInitialState((s)=>({
                                        ...s,
                                        currentTeam: undefined
                                    }));
                                _max.history.push('/user/team-select');
                            } catch (error) {
                                console.error('删除团队失败:', error);
                                _antd.message.error('删除团队失败');
                            } finally{
                                setDeleting(false);
                            }
                        }
                    });
                };
                const handleGoBack = ()=>{
                    _max.history.push('/user/team-select');
                };
                const formatDate = (dateString)=>{
                    return new Date(dateString).toLocaleDateString('zh-CN', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    });
                };
                const getTeamStatusColor = ()=>{
                    const memberCount = teamDetail.memberCount;
                    if (memberCount >= 10) return '#52c41a'; // 绿色 - 活跃
                    if (memberCount >= 5) return '#faad14'; // 橙色 - 正常
                    return '#1890ff'; // 蓝色 - 小团队
                };
                const getTeamStatusText = ()=>{
                    const memberCount = teamDetail.memberCount;
                    if (memberCount >= 10) return '活跃团队';
                    if (memberCount >= 5) return '正常团队';
                    return '小型团队';
                };
                // 创建下拉菜单项
                const menuItems = teamDetail.isCreator ? [
                    {
                        key: 'edit',
                        label: '编辑团队',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 147,
                            columnNumber: 13
                        }, this),
                        onClick: handleEdit
                    },
                    {
                        key: 'delete',
                        label: '删除团队',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 153,
                            columnNumber: 13
                        }, this),
                        onClick: handleDeleteTeam,
                        danger: true
                    }
                ] : [];
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        padding: '0 24px'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            style: {
                                marginBottom: 24,
                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                border: 'none',
                                borderRadius: 16
                            },
                            styles: {
                                body: {
                                    padding: '24px 32px'
                                }
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                    align: "middle",
                                    justify: "space-between",
                                    style: {
                                        marginBottom: 24
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            flex: "0 0 auto",
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "text",
                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ArrowLeftOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 177,
                                                    columnNumber: 21
                                                }, void 0),
                                                onClick: handleGoBack,
                                                style: {
                                                    color: 'rgba(255, 255, 255, 0.9)',
                                                    fontSize: 16,
                                                    padding: '8px 16px',
                                                    height: 'auto'
                                                },
                                                children: "返回"
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 175,
                                                columnNumber: 13
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 174,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            flex: "1 1 auto",
                                            style: {
                                                textAlign: 'center'
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                align: "center",
                                                direction: "vertical",
                                                size: 4,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    align: "center",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                            level: 2,
                                                            style: {
                                                                color: 'white',
                                                                margin: 0
                                                            },
                                                            children: teamDetail.name
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                            lineNumber: 194,
                                                            columnNumber: 17
                                                        }, this),
                                                        teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                lineNumber: 199,
                                                                columnNumber: 27
                                                            }, void 0),
                                                            color: "gold",
                                                            style: {
                                                                fontSize: 12
                                                            },
                                                            children: "管理员"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                            lineNumber: 198,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                                                            color: getTeamStatusColor(),
                                                            text: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    color: 'rgba(255, 255, 255, 0.8)'
                                                                },
                                                                children: getTeamStatusText()
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                lineNumber: 209,
                                                                columnNumber: 21
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                            lineNumber: 206,
                                                            columnNumber: 17
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 193,
                                                    columnNumber: 15
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 192,
                                                columnNumber: 13
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 191,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            flex: "0 0 auto",
                                            children: teamDetail.isCreator && menuItems.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                                menu: {
                                                    items: menuItems
                                                },
                                                placement: "bottomRight",
                                                trigger: [
                                                    'click'
                                                ],
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "text",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                        lineNumber: 228,
                                                        columnNumber: 25
                                                    }, void 0),
                                                    style: {
                                                        color: 'rgba(255, 255, 255, 0.9)',
                                                        fontSize: 18,
                                                        padding: '8px',
                                                        height: 'auto'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 226,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 221,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 219,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 172,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                    align: "middle",
                                    justify: "center",
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            size: "large",
                                            align: "center",
                                            direction: "vertical",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                    size: 80,
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                        lineNumber: 247,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    style: {
                                                        backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                                        color: 'white',
                                                        fontSize: 32
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 245,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                    style: {
                                                        color: 'rgba(255, 255, 255, 0.8)',
                                                        margin: 0,
                                                        maxWidth: 500,
                                                        textAlign: 'center'
                                                    },
                                                    ellipsis: {
                                                        rows: 2
                                                    },
                                                    children: teamDetail.description || '这个团队还没有描述'
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 254,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 244,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 243,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 242,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 162,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: [
                                16,
                                16
                            ],
                            style: {
                                marginBottom: 24
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "团队成员",
                                            value: teamDetail.memberCount,
                                            suffix: "人",
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                style: {
                                                    color: '#1890ff'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 278,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#1890ff'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 274,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 273,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 272,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "创建时间",
                                            value: formatDate(teamDetail.createdAt),
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                style: {
                                                    color: '#52c41a'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 288,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#52c41a',
                                                fontSize: 16
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 285,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 284,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 283,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "最后活动",
                                            value: formatDate(teamDetail.updatedAt),
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                style: {
                                                    color: '#faad14'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 298,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#faad14',
                                                fontSize: 16
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 295,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 294,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 293,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                textAlign: 'center'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    style: {
                                                        fontSize: 14
                                                    },
                                                    children: "团队活跃度"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 306,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        marginTop: 8
                                                    },
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                                        type: "circle",
                                                        size: 60,
                                                        percent: Math.min(teamDetail.memberCount * 10, 100),
                                                        strokeColor: getTeamStatusColor(),
                                                        format: ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 12,
                                                                    color: getTeamStatusColor()
                                                                },
                                                                children: teamDetail.memberCount >= 10 ? '高' : teamDetail.memberCount >= 5 ? '中' : '低'
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                lineNumber: 314,
                                                                columnNumber: 21
                                                            }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                        lineNumber: 308,
                                                        columnNumber: 17
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 307,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 305,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 304,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 303,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 271,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamMemberList.default, {
                            teamId: teamDetail.id,
                            isCreator: teamDetail.isCreator,
                            onMemberChange: onRefresh
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 327,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "编辑团队信息",
                            open: editModalVisible,
                            onCancel: ()=>setEditModalVisible(false),
                            footer: null,
                            width: 600,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: form,
                                layout: "vertical",
                                onFinish: handleUpdateTeam,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "团队名称",
                                        name: "name",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入团队名称'
                                            },
                                            {
                                                max: 50,
                                                message: '团队名称不能超过50个字符'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入团队名称"
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 356,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 348,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "团队描述",
                                        name: "description",
                                        rules: [
                                            {
                                                max: 200,
                                                message: '团队描述不能超过200个字符'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            rows: 4,
                                            placeholder: "请输入团队描述（可选）",
                                            showCount: true,
                                            maxLength: 200
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 365,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 358,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        style: {
                                            marginBottom: 0,
                                            textAlign: 'right'
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: ()=>setEditModalVisible(false),
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 374,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    loading: updating,
                                                    children: "保存"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 377,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 373,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 372,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                lineNumber: 343,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 336,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                    lineNumber: 160,
                    columnNumber: 5
                }, this);
            };
            _s(EnhancedTeamDetail, "UalB0vhOvdT9JI/tG4E1rE8Z8wk=", false, function() {
                return [
                    _antd.Form.useForm,
                    _max.useModel
                ];
            });
            _c = EnhancedTeamDetail;
            var _default = EnhancedTeamDetail;
            var _c;
            $RefreshReg$(_c, "EnhancedTeamDetail");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '12813097303458974156';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/create/index.tsx": [
            "p__team__create__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "common",
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=umi.9835540946890387120.hot-update.js.map